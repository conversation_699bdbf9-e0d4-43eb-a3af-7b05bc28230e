name: <PERSON><PERSON> Docker

on:
  push:
    branches:
      - main
    tags:
      - "v*"
    paths:
      - "mcp_server/**"
      - ".github/workflows/slmcp-docker.yml"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERIO_USERNAME }}
          password: ${{ secrets.DOCKERIO_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: ./mcp_server
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            chaitin/safeline-mcp:latest
            chaitin/safeline-mcp:${{ github.ref_name }}
          cache-from: type=registry,ref=chaitin/safeline-mcp:buildcache
          cache-to: type=registry,ref=chaitin/safeline-mcp:buildcache,mode=max
