package constants

const (
	ProtocolHTTP  = 0
	ProtocolHTTPS = 1
	ProtocolHTTP2 = 2
)

var (
	HTTPProtocol = map[int]string{
		ProtocolHTTP:  "http",
		ProtocolHTTPS: "https",
		// 在 WAF 中开启 HTTP2 的前提是开启 TLS，所以这里先把 HTTP2 都兼容显示为 HTTPS
		ProtocolHTTP2: "https",
	}

	AttackType = map[int]string{
		-4: "超长数据",
		-3: "黑名单",
		-2: "白名单",
		-1: "非攻击",
		0:  "SQL 注入",
		1:  "XSS",
		2:  "CSRF",
		3:  "SSRF",
		4:  "拒绝服务",
		5:  "后门",
		6:  "反序列化",
		7:  "代码执行",
		8:  "代码注入",
		9:  "命令注入",
		10: "文件上传",
		11: "文件包含",
		12: "重定向",
		13: "权限不当",
		14: "信息泄露",
		15: "未授权访问",
		16: "不安全的配置",
		17: "XXE",
		18: "XPath 注入",
		19: "LDAP 注入",
		20: "目录穿越",
		21: "扫描器",
		22: "水平权限绕过",
		23: "垂直权限绕过",
		24: "文件修改",
		25: "文件读取",
		26: "文件删除",
		27: "逻辑错误",
		28: "CRLF 注入",
		29: "模板注入",
		30: "点击劫持",
		31: "缓冲区溢出",
		32: "整数溢出",
		33: "格式化字符串",
		34: "条件竞争",
		35: "HTTP 协议违规",
		61: "超时",
		62: "未知",
		63: "威胁情报",
		64: "Cookie 篡改",
	}

	CountryCode = map[string]string{
		"CN": "中国",
		"AD": "安道尔",
		"AE": "阿联酋",
		"AF": "阿富汗",
		"AG": "安提瓜和巴布达",
		"AI": "安圭拉",
		"AL": "阿尔巴尼亚",
		"AM": "亚美尼亚",
		"AO": "安哥拉",
		"AQ": "南极洲",
		"AR": "阿根廷",
		"AS": "美属萨摩亚",
		"AT": "奥地利",
		"AU": "澳大利亚",
		"AW": "阿鲁巴",
		"AX": "奥兰",
		"AZ": "阿塞拜疆",
		"BA": "波斯尼亚和黑塞哥维那",
		"BB": "巴巴多斯",
		"BD": "孟加拉国",
		"BE": "比利时",
		"BF": "布基纳法索",
		"BG": "保加利亚",
		"BH": "巴林",
		"BI": "布隆迪",
		"BJ": "贝宁",
		"BL": "圣巴泰勒米",
		"BM": "百慕大",
		"BN": "文莱",
		"BO": "玻利维亚",
		"BQ": "加勒比荷兰",
		"BR": "巴西",
		"BS": "巴哈马",
		"BT": "不丹",
		"BV": "布韦岛",
		"BW": "博茨瓦纳",
		"BY": "白俄罗斯",
		"BZ": "伯利兹",
		"CA": "加拿大",
		"CC": "科科斯（基林）群岛",
		"CD": "刚果（金）",
		"CF": "中非",
		"CG": "刚果（布）",
		"CH": "瑞士",
		"CI": "科特迪瓦",
		"CK": "库克群岛",
		"CL": "智利",
		"CM": "喀麦隆",
		"CO": "哥伦比亚",
		"CR": "哥斯达黎加",
		"CU": "古巴",
		"CV": "佛得角",
		"CW": "库拉索",
		"CX": "圣诞岛",
		"CY": "塞浦路斯",
		"CZ": "捷克",
		"DE": "德国",
		"DJ": "吉布提",
		"DK": "丹麦",
		"DM": "多米尼克",
		"DO": "多米尼加",
		"DZ": "阿尔及利亚",
		"EC": "厄瓜多尔",
		"EE": "爱沙尼亚",
		"EG": "埃及",
		"EH": "阿拉伯撒哈拉民主共和国",
		"ER": "厄立特里亚",
		"ES": "西班牙",
		"ET": "埃塞俄比亚",
		"FI": "芬兰",
		"FJ": "斐济",
		"FK": "福克兰群岛",
		"FM": "密克罗尼西亚联邦",
		"FO": "法罗群岛",
		"FR": "法国",
		"GA": "加蓬",
		"GB": "英国",
		"GD": "格林纳达",
		"GE": "格鲁吉亚",
		"GF": "法属圭亚那",
		"GG": "根西",
		"GH": "加纳",
		"GI": "直布罗陀",
		"GL": "格陵兰",
		"GM": "冈比亚",
		"GN": "几内亚",
		"GP": "瓜德罗普",
		"GQ": "赤道几内亚",
		"GR": "希腊",
		"GS": "南乔治亚和南桑威奇群岛",
		"GT": "危地马拉",
		"GU": "关岛",
		"GW": "几内亚比绍",
		"GY": "圭亚那",
		"HM": "赫德岛和麦克唐纳群岛",
		"HN": "洪都拉斯",
		"HR": "克罗地亚",
		"HT": "海地",
		"HU": "匈牙利",
		"ID": "印尼",
		"IE": "爱尔兰",
		"IL": "以色列",
		"IM": "马恩岛",
		"IN": "印度",
		"IO": "英属印度洋领地",
		"IQ": "伊拉克",
		"IR": "伊朗",
		"IS": "冰岛",
		"IT": "意大利",
		"JE": "泽西",
		"JM": "牙买加",
		"JO": "约旦",
		"JP": "日本",
		"KE": "肯尼亚",
		"KG": "吉尔吉斯斯坦",
		"KH": "柬埔寨",
		"KI": "基里巴斯",
		"KM": "科摩罗",
		"KN": "圣基茨和尼维斯",
		"KP": "朝鲜",
		"KR": "韩国",
		"KW": "科威特",
		"KY": "开曼群岛",
		"KZ": "哈萨克斯坦",
		"LA": "老挝",
		"LB": "黎巴嫩",
		"LC": "圣卢西亚",
		"LI": "列支敦士登",
		"LK": "斯里兰卡",
		"LR": "利比里亚",
		"LS": "莱索托",
		"LT": "立陶宛",
		"LU": "卢森堡",
		"LV": "拉脱维亚",
		"LY": "利比亚",
		"MA": "摩洛哥",
		"MC": "摩纳哥",
		"MD": "摩尔多瓦",
		"ME": "黑山",
		"MF": "法属圣马丁",
		"MG": "马达加斯加",
		"MH": "马绍尔群岛",
		"MK": "马其顿",
		"ML": "马里",
		"MM": "缅甸",
		"MN": "蒙古",
		"MP": "北马里亚纳群岛",
		"MQ": "马提尼克",
		"MR": "毛里塔尼亚",
		"MS": "蒙特塞拉特",
		"MT": "马耳他",
		"MU": "毛里求斯",
		"MV": "马尔代夫",
		"MW": "马拉维",
		"MX": "墨西哥",
		"MY": "马来西亚",
		"MZ": "莫桑比克",
		"NA": "纳米比亚",
		"NC": "新喀里多尼亚",
		"NE": "尼日尔",
		"NF": "诺福克岛",
		"NG": "尼日利亚",
		"NI": "尼加拉瓜",
		"NL": "荷兰",
		"NO": "挪威",
		"NP": "尼泊尔",
		"NR": "瑙鲁",
		"NU": "纽埃",
		"NZ": "新西兰",
		"OM": "阿曼",
		"PA": "巴拿马",
		"PE": "秘鲁",
		"PF": "法属波利尼西亚",
		"PG": "巴布亚新几内亚",
		"PH": "菲律宾",
		"PK": "巴基斯坦",
		"PL": "波兰",
		"PM": "圣皮埃尔和密克隆",
		"PN": "皮特凯恩群岛",
		"PR": "波多黎各",
		"PS": "巴勒斯坦",
		"PT": "葡萄牙",
		"PW": "帕劳",
		"PY": "巴拉圭",
		"QA": "卡塔尔",
		"RE": "留尼汪",
		"RO": "罗马尼亚",
		"RS": "塞尔维亚",
		"RU": "俄罗斯",
		"RW": "卢旺达",
		"SA": "沙特阿拉伯",
		"SB": "所罗门群岛",
		"SC": "塞舌尔",
		"SD": "苏丹",
		"SE": "瑞典",
		"SG": "新加坡",
		"SH": "圣赫勒拿",
		"SI": "斯洛文尼亚",
		"SJ": "挪威",
		"SK": "斯洛伐克",
		"SL": "塞拉利昂",
		"SM": "圣马力诺",
		"SN": "塞内加尔",
		"SO": "索马里",
		"SR": "苏里南",
		"SS": "南苏丹",
		"ST": "圣多美和普林西比",
		"SV": "萨尔瓦多",
		"SX": "荷属圣马丁",
		"SY": "叙利亚",
		"SZ": "斯威士兰",
		"TC": "特克斯和凯科斯群岛",
		"TD": "乍得",
		"TF": "法属南方和南极洲领地",
		"TG": "多哥",
		"TH": "泰国",
		"TJ": "塔吉克斯坦",
		"TK": "托克劳",
		"TL": "东帝汶",
		"TM": "土库曼斯坦",
		"TN": "突尼斯",
		"TO": "汤加",
		"TR": "土耳其",
		"TT": "特立尼达和多巴哥",
		"TV": "图瓦卢",
		"TZ": "坦桑尼亚",
		"UA": "乌克兰",
		"UG": "乌干达",
		"UM": "美国本土外小岛屿",
		"US": "美国",
		"UY": "乌拉圭",
		"UZ": "乌兹别克斯坦",
		"VA": "梵蒂冈",
		"VC": "圣文森特和格林纳丁斯",
		"VE": "委内瑞拉",
		"VG": "英属维尔京群岛",
		"VI": "美属维尔京群岛",
		"VN": "越南",
		"VU": "瓦努阿图",
		"WF": "瓦利斯和富图纳",
		"WS": "萨摩亚",
		"YE": "也门",
		"YT": "马约特",
		"ZA": "南非",
		"ZM": "赞比亚",
		"ZW": "津巴布韦",
	}

	RuleModule = map[string]string{
		"m_sqli":               "SQL 注入检测模块",
		"m_xss":                "XSS 检测模块",
		"m_csrf":               "CSRF 检测模块",
		"m_ssrf":               "SSRF 检测模块",
		"m_php_unserialize":    "PHP 反序列化检测模块",
		"m_java_unserialize":   "Java 反序列化检测模块",
		"m_file_upload":        "文件上传攻击检测模块",
		"m_file_include":       "文件包含攻击检测模块",
		"m_php_code_injection": "PHP 代码注入检测模块",
		"m_java":               "Java 代码注入检测模块",
		"m_cmd_injection":      "命令注入检测模块",
		"m_response":           "服务器响应检测模块",
		"m_scanner":            "机器人检测模块",
		"m_http":               "畸形 HTTP 协议检测模块",
		"m_asp_code_injection": "ASP 代码注入检测模块",
		"m_ssti":               "模板注入检测模块",
		"m_rule":               "通用漏洞模块",
		"m_timeout":            "", // 检测超时
		"whitelist":            "白名单",
		"blacklist":            "黑名单",
	}

	// RuleReason TODO: get from libfusion.so, refer to skyview `Fusion.get_rule_detail_dict()`
	RuleReason = map[string]string{
		"6f4922f45568161a8cdf4ad2299f6d23": "访问测试文件的请求",
		"c6b99e08e56911ecb18f00155d694977": "[HW2021] 深信服终端检测平台远程命令执行漏洞(CNVD-2020-46552)",
		"c9a2f2f5b1035a1ca3a5aa776914e393": "[HW2020] GitLab 文件读写 (CVE-2017-0915, CVE-2016-9086)",
		"97e264c2235c4ad0aa61ecd68fa53351": "jenkins 管理员权限开放 (CVE-2018-1999001)",
		"ee41775801ab11ed90a200155ddb8f4e": "Apache HTTPD SSRF (CVE-2021-40438)",
		"33e75ff09dd601bbe69f351039152189": "访问系统文件的请求",
		"1c383cd30b7c298ab50293adfecb7b18": "Struts2 Java 代码注入漏洞",
		"eff4046e0b0411ed976f00155dd454dd": "蓝凌OA漏洞",
		"a1d0c6e83f027327d8461063f4ac58a6": "webshell",
		"d92ebbf7683c5cafbc74456c73bc5d0c": "[HW2020] JIRA OAuth SSRF (CVE-2017-9506)",
		"dbc22aa2fce011eca0e800163e345065": "[HW2020] Nginx range filter overflow (CVE-2017-7529)",
		"ae971210f5f411ecae9200163e345065": "[HW2022] VMware 认证绕过漏洞 (CVE-2022-22972)",
		"5af4619c46045568b4d701ad5208eee0": "[HW2020] Druid 未授权访问",
		"fbd7939d674997cdb4692d34de8633c4": "Jackson 反序列化 (CVE-2017-17485)",
		"63400573dc155253869bc90e341708e9": "[HW2020] Apache Spark 未授权访问",
		"37274a94eadf11eca1a600155dcfc445": "[HW2022] Apache Solr SSRF漏洞 (CVE-2021-27905)",
		"c4ca4238a0b923820dcc509a6f75849b": "PHP 代码泄露",
		"3416a75f4cea9109507cacd8e2f2aefc": "phpinfo 泄露",
		"c9f0f895fb98ab9159f51fd0297e236d": "访问敏感文件的请求",
		"36564acfab005dcda413a0b489ce8a02": "[HW2020] ffmpeg SSRF (CVE-2016-1898)",
		"31a7ad75521152eca43e7eb15065e850": "[HW2020] Kibana远程代码执行(CVE-2019-7609)",
		"03d0598da679b4d68089730de888a945": "Laravel Debug 远程代码执行 (CVE-2021-3129)",
		"34173cb38f07f89ddbebc2ac9128303f": "XML 实体注入漏洞",
		"09afe350746411eda37500163e12b978": "Apache Kylin 未授权配置 (CVE-2020-13927)",
		"4096d530f60c11eca0ad00163e345065": "[HW2020] Horde Groupware Webmail Edition 远程命令执行漏洞",
		"c6b57d8ce56911ec980b00155d694977": "[HW2021] F5 BIG-IP 远程代码执行漏洞(CVE-2020-5902)",
		"8964339e01b411ed9bdc00163e345065": "Spring Cloud Config 路径穿越漏洞 (CVE-2020-5405)",
		"e336d501c94e556d8fa3f6df584e88e2": "[HW2020] Spring WebFlow RCE (CVE-2017-4971)",
		"c6b9fa2ee56911ecb64f00155d694977": "[HW2021] 通达OA权限提升漏洞(11.5.200417 之前的版本)",
		"8f1c07fe747c11ed8b7c00163e12b978": "Microsoft Exchange 远程代码执行 (CVE-2020-0688)",
		"3ef815416f775098fe977004015c6193": "Apache Solr 远程代码执行漏洞 (CVE-2019-0193)",
		"2e7f58dd541d5c478fc12bc8a64bef41": "[HW2020] S2-005 (2) (CVE-2010-1870)",
		"1ff1de774005f8da13f42943881c655f": "DEDECMS add suffix",
		"458c1b2af1f911ecafdb00163e345065": "[HW2021] Sitecore XP远程代码执行漏洞(CVE-2021-42237)",
		"ac3d6ee2714b11ed87ba00163e12b978": "通达OA V11.x远程代码执行漏洞",
		"9fa20cc8eabf11ec808600155dcfc445": "[HW2022] 360天擎信息泄露",
		"2a1f6e4292ee30c1a92d3e8333471534": "Jackson 反序列化",
		"e404c364f1f511ec9cc500163e345065": "[HW2021] Zabbix 5.0.17-Remote Code Execution (RCE) (Authenticated)",
		"c6b8cd66e56911ec8cc100155d694977": "[HW2021] 宝塔面板数据库管理未授权访问漏洞(Linux正式版7.4.2、Linux测试版7.5.13、Windows正式版6.8)",
		"3c27c530751511edbaf200163e12b978": "Citrix 未授权访问 ",
		"22aaa784da604abeb19b0aeee2b3cc6d": "Jenkins 非预期方法调用漏洞(SECURITY-595)",
		"a5bfc9e07964f8dddeb95fc584cd965d": "Java 畸形 double 数据 DOS 漏洞",
		"c6babe8ce56911eca76200155d694977": "[HW2021] 致远OA 文件上传漏洞(V8.0、V7.1、V7.1SP1、V7.0、V7.0SP1、V7.0SP2、V7.0SP3、V6.0、V6.1SP1、V6.1SP2、V5.x)",
		"d7e82c628f96541c924637f711555266": "[HW2020] PHPStudy 命令执行",
		"7f39f8317fbdb1988ef4c628eba02591": "访问 IIS 短文件名 / 文件夹",
		"44f683a84163b3523afe57c2e008bc8c": "扫描 IIS 短文件名 / 文件夹",
		"c38555f8746411eda4a400163e12b978": "快排CMS 未授权访问 ",
		"866cbe77d2884225a8fa19793cc8ad51": "ghostscript 命令执行 (CVE-2018-19475)",
		"17400b535f9f5db5a8424265ed8cc38c": "[HW2020] JeeCMS SSRF",
		"774b89ca853942cabc8b94bfff46d73c": "ECShop 2.x-3.x 远程代码执行漏洞",
		"b801a4a10a22422dff885812a5d0d417": "科蓝反序列化漏洞",
		"6c991e5e23215c589a86f13ae800c616": "[HW2020] Couchdb 垂直越权 (1) (CVE-2017-12635)",
		"7d5a620cf69311ecbf8400163e345065": "[HW2020] ShardingShpere远程命令执行漏洞 (CVE-2020-1947)",
		"a26afe82eade11ecb85f00155dcfc445": "[HW2022] Alibaba Nacos认证绕过",
		"3343e2def60b11ec964400163e345065": "[HW2021] 用友ERP-NC 任意文件读取漏洞 (2021-?-?)",
		"09189cf61c0e4774a4d8af7bfb0cf6de": "WebLogic wls9-async 和 wls-wsat 反序列化 (CNVD-C-2019-48814)",
		"738430de6f054031a7597c00eb1fd061": "Drupal Mail 命令注入",
		"2abf53869fd05cf5a69c11ea42d2cb54": "[HW2020] ActiveMQ 任意文件写入漏洞 (CVE-2016-3088)",
		"e2c420d928d4bf8ce0ff2ec19b371514": "Drupal 内核远程代码执行漏洞 (CVE-2018-7602)",
		"ac61a980eae411ec985100155dcfc445": "[HW2022] Zyxel NBG2105 身份验证绕过 (CVE-2021-3297)",
		"d67d8ab4f4c10bf22aa353e27879133c": "PHP168 代码执行漏洞",
		"e7629f3e750a11eda3a900163e12b978": "PbootCms v3.1.2 远程代码执行 ",
		"6d85a117690c32af50f9b47f98abf408": "骑士 CMS 远程代码执行",
		"4ebd2f6aee2811ec8e2600163e345065": "[HW2022] 齐治堡垒机任意用户登录漏洞",
		"688142e95d6d446ee901de2b4d087a5d": "F5 BIG-IP漏洞",
		"91d18ff6f2dc54618b1a28f15afe572f": "[HW2020] Couchdb 命令执行 (CVE-2017-12636)",
		"70efdf2ec9b086079795c442636b55fb": "访问管理后台的请求",
		"6d6bcd8ebc2c4d4a961ce2e8b4b3af67": "joomla 远程代码执行",
		"c6b4be74e56911eca25c00155d694977": "[HW2021] Apache Shiro身份认证绕过漏洞(CVE-2020-17523)",
		"1f0822d21d0a11edad0900163e345065": "[HW2020] Couchdb 垂直越权 (1) (CVE-2017-12635)",
		"2024042cf11511ecb17e00163e345065": "[HW2021] GoAhead Server 环境变量注入漏洞(CVE-2021-42342)",
		"abade038ebc211ec8a8800163e345065": "[HW2022] VMware SSRF XSS)",
		"32ac3f49feb65bada13cdf85722d237a": "[HW2020] Spring RCE (CVE-2018-1270)",
		"43ec517d68b6edd3015b3edc9a11367b": "Spring 框架漏洞",
		"6a48a52c11424cd68e89d26d212556c4": "ThinkPHP5 任意代码执行漏洞",
		"a5d9fe3b0172b4625a1fe6a41482aa8b": "Xstream 反序列化",
		"072b030ba126b2f4b2374f342be9ed44": "路径穿越攻击",
		"38529f8eebc211eca87c00163e345065": "[HW2022] VMware 任意文件读取",
		"3a3128d0e7e811ec909000155db4c628": "Java 代码注入",
		"e369853df766fa44e1ed0ff613f563bd": "Struts2 S2-020",
		"d3d9446802a44259755d38e6d163e820": "访问 Git 仓库的请求",
		"6e4b3bcf45295dcc9b1f3efef4353287": "[HW2020] JIRA SSRF (CVE-2019-8451)",
		"b58a180cf11111ec8a4600163e345065": "[HW2022] VMware 服务端模板注入漏洞(CVE-2022-22954)",
		"7cbbc409ec990f19c78c75bd1e06f215": "Jenkins 远程代码执行漏洞 (CVE-2018-1000861)",
		"c6b6f9d2e56911ec8e2e00155d694977": "[HW2021] phpStudy nginx 解析漏洞(phptsuy8.1.07的Nginx1.5.11版本)",
		"b4b143a12d0d5eeabef67b6597dc7cd9": "[HW2020] DedeCMS 密码重置漏洞",
		"028c274cfc4f11ecaedd00163e345065": "[HW2022] Oracle Access Manager RCE (CVE-2021-35587)",
		"1fc707c92da6519e9337f202ad1fd959": "[HW2020] 通达OA 前台任意用户登录",
		"2efd7872752f11ed97f600163e12b978": "XStream 反序列化代码执行 (CVE-2021-29505)",
		"9355da5d436e52cba6fd2012ad5a5832": "[HW2020] Spring Data Rest RCE (CVE-2017-8046)",
		"d09bf41544a3365a46c9077ebb5e35c3": "Jackson 反序列化 (CVE-2017-7525)",
		"c20ad4d76fe97759aa27a0c99bff6710": "访问 SVN 仓库的请求",
		"e1c17c16169b58078be071b37eda9f3c": "[HW2020] HFS RCE",
		"e4a2355fa844484bbdf799cc8795b415": "Apache Druid 远程命令执行漏洞",
		"72c04a1fbd6e5641bb33d0185dc36b9b": "[HW2020] Nexus 后台表达式注入 (CVE-2020-10199, CVE-2019-7238)",
		"c6b519e6e56911ecaa1c00155d694977": "[HW2021] Drupal任意PHP代码执行漏洞(CVE-2020-28948/28949)",
		"c81e728d9d4c2f636f067f89cc14862c": "访问敏感文件的请求",
		"a02582663945fa213788203624eb3b89": "Jackson-databind 反序列化 (CVE-2020-35790/CVE-2020-35491)",
		"45c48cce2e2d7fbdea1afc51c7c6ad26": "访问 Git 仓库的请求",
		"029e67ff376659daaea612ec5c3964ab": "[HW2020] Gitea LFS RCE",
		"e52769085be711ec859e00155dae391c": "Apache Log4j 远程命令执行漏洞",
		"19ca14e7ea6328a42e0eb13d585e4c22": "Java 代码注入漏洞 (针对 Struts 2 漏洞的通用防御)",
		"3c59dc048e8850243be8079a5c74d079": "CVE-2012-1823 PHP FastCGI 远程代码执行漏洞",
		"14766b63abf447ac808b92535dde9c09": "Gogs、Gitea 远程代码执行漏洞 (CVE-2018-18925)",
		"9bf31c7ff062936a96d3c8bd1f8f2ff3": "后门",
		"ae0196baf5f211ec98c400163e345065": "[HW2022] Sunlogin-11.0.0.33-RCE-1_获取token",
		"2838023a778dfaecdc212708f721b788": "访问备份或临时文件的请求",
		"110ad086eaff11ec87c500155d143f7b": "[HW2022] Gitlab exiftool 远程命令执行漏洞 (CVE-2021-22205)",
		"e3959e0e30494d6caa1e9b259073d96c": "南方数据管理员添加未授权访问漏洞",
		"98f13708210194c475687be6106a3b84": "Nginx code 解析漏洞",
		"887d1820a85a43cfbeee578a02f6a914": "fastjson 反序列化代码执行",
		"88a4a7227e81d064b551f52f7f51e6c8": "泛微 E-cology OA远程代码执行",
		"4c15abcbe6e8410aac559817925bfd11": "TBK DVR 验证绕过漏洞 (CVE-2018-9995)",
		"460072c82cca58999281247426c71bf8": "[HW2020] ffmpeg 文件读取 (CVE-2016-1897)",
		"96c4bc6a6adf4434b93d714106f4c67f": "ghostscript 任意文件读写 (CVE-2018-17961)",
		"9dbe327d8dd64f3a8d919cf19d5e3e00": "ThinkPHP 5.0.x _method 代码注入",
		"6512bd43d9caa6e02c990b0a82652dca": "访问 SVN 仓库的请求",
		"a12856ca51bb5ed69b6646dafd605e7c": "[HW2020] Apache Shiro 反序列化攻击 (CVE-2016-4437)",
		"9473dea2eb144fee9ddeac33b49358b9": "Nexus Repository Manager 3 远程代码执行漏洞(CVE-2019-7238)",
		"eccbc87e4b5ce2fe28308fd9f2a7baf3": "Apache 目录遍历",
		"e799871cf6b653db94b759b79deefe5d": "[HW2020] ImageMagick RCE (CVE-2016-3714)",
		"ca037bccfa7c5e7b822ea1889ba7be47": "[HW2020] MetInfo 前台 SQL 注入 (CNVD-2018-20024)",
		"638d9bb801b611eda81e00163e345065": "Atlassian Jira 服务端请求伪造漏洞 (CVE-2022-26135)",
		"8ef24d7501e85d1fa3ce4016dbf297d1": "[HW2020] Webmin RCE (CVE-2019-15107)",
		"81f360f5033c55179d34b37b61389503": "[HW2020] Palo Alto GlobalProtect SSL RCE (CVE-2019-1579)",
		"c374fb01134a4213be9ba67539523162": "IIS 扩展名漏洞 (CVE-2009-4444)",
		"21203602eafd11ec8dc300155d143f7b": "[HW2022] F5 BIG-IP 远程代码执行漏洞 (CVE-2021-22986)",
		"2cba1bd57c9603c07ff4f09f8e4fb0f9": "Apache AXIS 远程命令执行漏洞",
		"a0ff28f2f5f111eca2de00163e345065": "[HW2022] Weblogic Server 信息泄漏漏洞利用",
		"d645920e395fedad7bbbed0eca3fe2e0": "DEDECMS SQL 注入漏洞",
		"2b6e15ac747811eda03000163e12b978": "Apache DolphinScheduler 远程代码执行 (CVE-2020-11974)",
		"1f5449318bbc38d200fb9f3bcf87a50f": "WebLogic console 远程代码执行漏洞 (CVE-2020-14882)",
		"02e74f10e0327ad868d138f2b4fdd6f0": "利用 Apache 解析漏洞来执行 PHP 脚本",
		"3ff4aed51a7c506087c0b9df1b6ac0d3": "[HW2020] Couchdb 垂直越权 (2) (CVE-2017-12635)",
		"933cbfbbc71c417ab9d757e5dc4be4d4": "Jenkins Accept-Language 信息泄露 (CVE-2018-1999002)",
		"182be0c5cdcd5072bb1864cdee4d3d6e": "Struts2 S2-016",
		"6af6b17ceae711ecbd5100155dcfc445": "[HW2022] 三星 WLAN AP WEA453e路由器 RCE (query)",
		"7f41d19e081411edaca100163e345065": "[HW2022] 三星 WLAN AP WEA453e路由器 RCE (form)",
		"cfcd208495d565ef66e7dff9f98764da": "JSP 代码泄露",
		"3b57f3413df14b2380ae8e483cb0dcd5": "Drupalgeddon2 - Drupal核心远程代码执行",
		"6364d3f0f495b6ab9dcf8d3b5c6e0b01": "Struts2 S2-005",
		"a98e8c0cf12211ecb1d800163e345065": "[HW2021] VMware vCenter Server 远程代码执行漏洞(CVE-2021-21985)",
		"b42530a1ea025fa78bb0c6c7d2e1ed95": "[HW2020] PHP FPM RCE (CVE-2019-11043)",
		"bbe199e1efec469588fde16dce75df36": "coremail 信息泄露",
		"c6b7547ce56911ec9d3d00155d694977": "[HW2021] SaltStack多个高危漏洞(CVE-2020-16846,CVE-2020-17490,CVE-2020-25592)",
		"c6b7ae5ee56911ec9d1600155d694977": "[HW2021] SAP NetWeaver AS JAVA 高危漏洞(CVE-2020-6287)",
		"65d86160f1f711ec971800163e345065": "[HW2021] Total.js框架远程代码执行漏洞(CVE-2021-23389)",
		"aa4755eb0fdf47ec91e8f285613e7954": "Atlassian 漏洞",
		"c6b5dc64e56911ecbe7300155d694977": "[HW2021] Microsoft Exchange远程代码执行漏洞(CVE-2020-16875)",
		"b6f76f653ded54b494292eb6c285d464": "[HW2020] WebLogic 弱口令",
		"9778d5d219c5080b9a6a17bef029331c": "Apache Solr 远程代码执行漏洞 (CVE-2019-0192)",
		"c6bb1f3ae56911ec93a500155d694977": "[HW2021] WebSphere 权限提升漏洞(CVE-2020-4276)",
		"c6ba5e10e56911ec8f6d00155d694977": "[HW2021] 微软 SQL Server 报表服务远程代码执行漏洞(CVE-2020-0618)",
		"061de838eabf11ec8ee500155dcfc445": "[HW2022] 360天擎SQL注入漏洞",
		"6ea9ab1baa0efb9e19094440c317e21b": "XML 实体注入漏洞",
		"039c966635bd4ac286553f55d9457615": "dedecms 5.7 升级时间泄露",
		"b6d767d2f8ed5d21a44b0e5886680cb9": "DEDECMS add suffix",
		"f033ab37c30201f73f142449d037028d": "ActiveMQ 任意文件写入漏洞 (CVE-2016-3088)",
		"558fce096ca65beb8f7ca7d4b2bf6b4f": "[HW2020] ElasticSearch 命令执行 (CVE-2015-1427)",
		"b53b3a3d6ab90ce0268229151c9bde11": "代码注入漏洞 (针对 WordPress)",
		"c6b4239ce56911ec8b8a00155d694977": "[HW2021] Apache Shiro 权限绕过漏洞(CVE-2020-13933)",
		"2408e35abe40580286b6d9106b43357f": "[HW2020] S2-005 (1) (CVE-2010-1870)",
		"724390fd0bf29ab3adb92ee026474fe1": "Jackson-databind 反序列化 (CVE-2020-36179~CVE-2020-36189)",
		"ef268f2b31fd5943bd1882e2218e9924": "[HW2020] Tomcat RCE (CVE-2019-0232)",
		"d49bb7ebe41146deb48138f28d220c7f": "可疑远程调用协议",
		"aab3238922bcc25a6f606eb525ffdc56": "phpinfo 泄露",
		"582e036cee2811ec90c700163e345065": "[HW2022] H3C SecPath运维审计系统任意用户登录漏洞",
		"25d513abf19740b083f2262270c0a281": "PHPCMS v9.6.0 任意文件上传",
		"ab299838fbe1584d843fc004b0034e81": "[HW2020] GoAhead RCE (CVE-2017-17562)",
		"c6b81b78e56911ec989800155d694977": "[HW2021] SolarWinds 远程代码执行漏洞(CVE-2020-10148)",
		"daf7d4a0116711ed8d9c00163e35b03e": "Seacms php代码注入",
		"c6b93e5ee56911ec8e1500155d694977": "[HW2021] 深信服终端检测平台任意用户登录(CNVD-2020-46552)",
		"95a4035353ed405ba3418698fc06b544": "spring 目录遍历 (cve-2014-3625)",
		"eab53f221ee611edb57e00163e345065": "VMware Workspace ONE UEM SSRF (CVE-2021-22054)",
		"fca34064ebc211ec9b9900163e345065": "[HW2022] VMware vRealize SSRF (CVE-2021-21975)",
		"c51ce410c124a10e0db5e4b97fc2af39": "PHPSpy 后门",
		"2f7e97b9204b5239b089c5ab83242519": "[HW2020] ElasticSearch 未授权访问",
		"6e34a9cb6f5b43e4973b170e626a2f77": "Apache APISIX lua 远程代码执行",
		"299ce1c43af85c7987d8ddef915888a8": "[HW2020] Zimbra RCE",
		"529d97a8f5d711eca1e700163e345065": "[HW2022] Weblogic Server 信息泄漏漏洞利用 (CVE-2022-21371)",
		"49234868ee1f11ec833b00155de98ae1": "[HW2022] AVTECH监控后台命令注入漏洞",
		"1f0e3dad99908345f7439f8ffabdffc4": "PHP FastCGI 解析漏洞",
	}
)
