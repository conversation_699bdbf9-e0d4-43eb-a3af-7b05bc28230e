module chaitin.cn/patronus/safeline-2/management/tcontrollerd

go 1.18

require (
	chaitin.cn/dev/go/errors v0.0.0-20210324055134-dc5247602af6
	chaitin.cn/dev/go/log v0.0.0-20221220104336-05125760b10c
	chaitin.cn/dev/go/settings v0.0.0-20221220104336-05125760b10c
	github.com/robfig/cron/v3 v3.0.1
	github.com/sirupsen/logrus v1.4.2
	google.golang.org/grpc v1.39.0-dev
	google.golang.org/protobuf v1.28.1
)

require (
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.1 // indirect
	golang.org/x/net v0.8.0 // indirect
	golang.org/x/sys v0.6.0 // indirect
	golang.org/x/text v0.8.0 // indirect
	golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543 // indirect
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013 // indirect
	gopkg.in/yaml.v2 v2.2.7 // indirect
)
