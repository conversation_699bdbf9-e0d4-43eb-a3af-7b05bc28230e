name: Bug
description: Report a bug
# Create a report to help us improve
title: "[Bug] "

body:
  - type: markdown
    attributes:
      value: |
        Please search the [open issues](https://github.com/chaitin/SafeLine/issues) and [discussion](https://github.com/chaitin/SafeLine/discussions) for duplicate issue first. 
        The more information you share, the faster we can identify and fix the bug.
      # Please check for duplicate issue first.
  - type: textarea
    id: Description
    attributes:
      label: What happened?
      # Describe the bug
    validations:
      required: false
  - type: textarea
    id: Reproduce
    attributes:
      label: How we reproduce?
      description: |
        Reports cannot be reproduced will Most likely be closed.
      # To Reproduce
      value: |
        1. ...
        2. ...
        3. ...
  - type: textarea
    id: Expected
    attributes:
      label: Expected behavior
      # placeholder: |
      # Descript what you expected to happen.
      # Expected behavior. Descript what you expected to happen.
  - type: textarea
    id: Errorlog
    attributes:
      label: Error log
      placeholder: |
        Paste the error logs if any.
      # Expected behavior. Descript what you expected to happen.

