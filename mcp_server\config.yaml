# Server Configuration
server:
  name: "SafeLine MCP Server"
  version: "1.0.0"
  # Can be overridden by environment variable LISTEN_PORT
  port: 5678
  # Can be overridden by environment variable LISTEN_ADDRESS
  host: "0.0.0.0"
  # Can be overridden by environment variable SAFELINE_SECRET
  secret: "" # Secret for SSE server
# Logger Configuration
logger:
  level: "info" # Log level: debug, info, warn, error
  file_path: "" # Log file path
  console: true # Whether to output to console
  caller: false # Whether to record caller information
  development: true # Whether to use development mode

# API Configuration
api:
  # Can be overridden by environment variable SAFELINE_ADDRESS
  base_url: "" # API service address
  # Can be overridden by environment variable SAFELINE_API_TOKEN
  token: "" # Authentication token
  timeout: 30 # Timeout in seconds
  debug: false # Whether to enable debug mode
  insecure_skip_verify: true # Whether to skip certificate verification
